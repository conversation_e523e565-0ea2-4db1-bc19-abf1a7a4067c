<?php
/**
 * Staff Create Payment
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/Order.php';
require_once '../../app/models/Table.php';

// Check if user is staff
if (!is_staff()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$orderModel = new Order($conn);
$tableModel = new Table($conn);

// Get current user
$currentUser = get_logged_in_user();

// Get order ID from URL
$orderId = (int)($_GET['order_id'] ?? 0);

if (!$orderId) {
    set_error_message('Không tìm thấy đơn hàng');
    redirect(STAFF_URL . '/payments/index.php');
}

// Get order details
$order = $orderModel->getOrderWithDetails($orderId);

if (!$order || $order['user_id'] != $currentUser['user_id']) {
    set_error_message('Bạn không có quyền truy cập đơn hàng này');
    redirect(STAFF_URL . '/payments/index.php');
}

if ($order['status'] !== 'completed') {
    set_error_message('Chỉ có thể thanh toán cho đơn hàng đã hoàn thành');
    redirect(STAFF_URL . '/orders/view.php?id=' . $orderId);
}

// Check if payment already exists
$existingPayment = $orderModel->query(
    "SELECT * FROM payments WHERE order_id = :order_id",
    [':order_id' => $orderId]
);

if (!empty($existingPayment) && $existingPayment[0]['payment_status'] === 'completed') {
    set_error_message('Đơn hàng này đã được thanh toán');
    redirect(STAFF_URL . '/orders/view.php?id=' . $orderId);
}

// Process payment form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        set_error_message('Token bảo mật không hợp lệ');
    } else {
        $paymentMethod = sanitize($_POST['payment_method'] ?? '');
        $paymentAmount = (float)($_POST['payment_amount'] ?? 0);
        $notes = sanitize($_POST['notes'] ?? '');

        // Validation
        $errors = [];
        
        if (!in_array($paymentMethod, ['cash', 'credit_card', 'debit_card', 'mobile_payment'])) {
            $errors[] = 'Vui lòng chọn phương thức thanh toán hợp lệ';
        }

        if ($paymentAmount <= 0) {
            $errors[] = 'Số tiền thanh toán phải lớn hơn 0';
        }

        if ($paymentAmount != $order['final_amount']) {
            $errors[] = 'Số tiền thanh toán phải bằng tổng tiền đơn hàng';
        }

        if (empty($errors)) {
            try {
                $orderModel->beginTransaction();

                // Create or update payment
                if (!empty($existingPayment)) {
                    // Update existing payment
                    $sql = "UPDATE payments SET 
                            payment_amount = :amount,
                            payment_method = :method,
                            payment_status = 'completed',
                            payment_date = NOW(),
                            notes = :notes
                            WHERE order_id = :order_id";
                    
                    $stmt = $conn->prepare($sql);
                    $stmt->bindValue(':amount', $paymentAmount);
                    $stmt->bindValue(':method', $paymentMethod);
                    $stmt->bindValue(':notes', $notes);
                    $stmt->bindValue(':order_id', $orderId);
                    $result = $stmt->execute();
                } else {
                    // Create new payment
                    $sql = "INSERT INTO payments (order_id, payment_amount, payment_method, payment_status, received_by, notes) 
                            VALUES (:order_id, :amount, :method, 'completed', :received_by, :notes)";
                    
                    $stmt = $conn->prepare($sql);
                    $stmt->bindValue(':order_id', $orderId);
                    $stmt->bindValue(':amount', $paymentAmount);
                    $stmt->bindValue(':method', $paymentMethod);
                    $stmt->bindValue(':received_by', $currentUser['user_id']);
                    $stmt->bindValue(':notes', $notes);
                    $result = $stmt->execute();
                }

                if ($result) {
                    // Update table status to available
                    $tableModel->update($order['table_id'], ['status' => 'available']);
                    
                    $orderModel->commit();
                    set_success_message('Thanh toán đã được xử lý thành công');
                    redirect(STAFF_URL . '/payments/index.php');
                } else {
                    $orderModel->rollback();
                    set_error_message('Có lỗi xảy ra khi xử lý thanh toán');
                }
            } catch (Exception $e) {
                $orderModel->rollback();
                set_error_message('Có lỗi xảy ra: ' . $e->getMessage());
            }
        } else {
            foreach ($errors as $error) {
                set_error_message($error);
            }
        }
    }
}

// Set page title
$pageTitle = 'Thanh toán đơn hàng #' . $orderId;

// Include content in layout
$content = VIEWS_DIR . '/staff/payments/create.php';
include_once VIEWS_DIR . '/layouts/main.php';
