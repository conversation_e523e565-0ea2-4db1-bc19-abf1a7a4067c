
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-0">Chào mừng, <?= htmlspecialchars($currentUser['full_name']) ?>!</h1>
            <p class="text-muted">Bảng điều khiển nhân viên - <?= date('d/m/Y') ?></p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="<?= STAFF_URL ?>/orders/create.php" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Tạo đơn hàng mới
                </a>
                <a href="<?= STAFF_URL ?>/tables/index.php" class="btn btn-outline-primary">
                    <i class="fas fa-chair me-1"></i> Q<PERSON><PERSON><PERSON> lý bàn
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Đơn hàng hôm nay</h5>
                            <h2 class="mb-0 fw-bold"><?= count($staffOrders) ?></h2>
                        </div>
                        <div class="bg-primary rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-receipt fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Đơn đang xử lý</h5>
                            <h2 class="mb-0 fw-bold"><?= count($activeOrders) ?></h2>
                        </div>
                        <div class="bg-warning rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-clock fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Doanh thu hôm nay</h5>
                            <h2 class="mb-0 fw-bold"><?= format_currency($dailyRevenue['total_revenue'] ?? 0) ?></h2>
                        </div>
                        <div class="bg-success rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-dollar-sign fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Bàn trống</h5>
                            <h2 class="mb-0 fw-bold"><?= $tableStatus['available'] ?? 0 ?></h2>
                        </div>
                        <div class="bg-info rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-chair fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Active Orders -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Đơn hàng đang xử lý</h5>
                    <a href="<?= STAFF_URL ?>/orders/index.php" class="btn btn-sm btn-outline-primary">
                        Xem tất cả
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($activeOrders)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Không có đơn hàng đang xử lý</p>
                            <a href="<?= STAFF_URL ?>/orders/create.php" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Tạo đơn hàng mới
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Bàn</th>
                                        <th>Tổng tiền</th>
                                        <th>Trạng thái</th>
                                        <th>Thời gian</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($activeOrders as $order): ?>
                                        <?php
                                        // Get table info
                                        $table = $tableModel->getById($order['table_id']);
                                        ?>
                                        <tr>
                                            <td><?= $order['order_id'] ?></td>
                                            <td><?= $table['table_number'] ?></td>
                                            <td><?= format_currency($order['final_amount']) ?></td>
                                            <td>
                                                <?php
                                                $statusClass = $order['status'] === 'pending' ? 'bg-warning' : 'bg-info';
                                                $statusText = $order['status'] === 'pending' ? 'Đang chờ' : 'Đang xử lý';
                                                ?>
                                                <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                            </td>
                                            <td><?= date('H:i', strtotime($order['order_date'])) ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= STAFF_URL ?>/orders/view.php?id=<?= $order['order_id'] ?>"
                                                       class="btn btn-outline-primary" title="Xem chi tiết">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= STAFF_URL ?>/orders/edit.php?id=<?= $order['order_id'] ?>"
                                                       class="btn btn-outline-warning" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Table Status -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Trạng thái bàn</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <?php foreach ($availableTables as $table): ?>
                            <div class="col-6">
                                <div class="card table-card table-available text-center">
                                    <div class="card-body p-2">
                                        <div class="table-number"><?= $table['table_number'] ?></div>
                                        <small class="text-muted"><?= $table['capacity'] ?> chỗ</small>
                                        <div class="mt-1">
                                            <span class="badge bg-success">Trống</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php foreach ($occupiedTables as $table): ?>
                            <div class="col-6">
                                <div class="card table-card table-occupied text-center">
                                    <div class="card-body p-2">
                                        <div class="table-number"><?= $table['table_number'] ?></div>
                                        <small class="text-muted"><?= $table['capacity'] ?> chỗ</small>
                                        <div class="mt-1">
                                            <span class="badge bg-danger">Đang sử dụng</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <a href="<?= STAFF_URL ?>/tables/index.php" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-chair me-1"></i> Quản lý bàn
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>