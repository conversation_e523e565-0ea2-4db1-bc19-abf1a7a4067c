<?php
require_once __DIR__ . '/../../config/config.php';
// <PERSON><PERSON><PERSON> tra quyền truy cập nhân viên
require_once __DIR__ . '/../../helpers.php';
if (!is_staff()) {
    set_error_message('Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/login.php');
}

// Lấy dữ liệu tổng quan cho nhân viên
require_once __DIR__ . '/../../models/Order.php';
require_once __DIR__ . '/../../models/Table.php';
require_once __DIR__ . '/../../models/FoodItem.php';
require_once __DIR__ . '/../../config/database.php';

$orderModel = new Order($conn);
$tableModel = new Table($conn);
$foodModel = new FoodItem($conn);

$user = get_logged_in_user();
$totalTables = $tableModel->count();
$activeOrders = $orderModel->getActiveOrders();
$myOrders = $orderModel->getActiveOrdersByUser($user['user_id']);
$tables = $tableModel->getAll();

include __DIR__ . '/../layouts/header.php';
?>
<div class="container-fluid py-4">
    <h1 class="mb-4">Bảng điều khiển nhân viên</h1>
    <div class="row mb-4">
        <div class="col-xl-4 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Tổng số bàn</h5>
                            <h2 class="mb-0 fw-bold"><?= $totalTables ?></h2>
                        </div>
                        <div class="bg-danger rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-chair fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Đơn hàng đang phục vụ</h5>
                            <h2 class="mb-0 fw-bold"><?= count($myOrders) ?></h2>
                        </div>
                        <div class="bg-success rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-receipt fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Tổng số đơn hàng</h5>
                            <h2 class="mb-0 fw-bold"><?= count($activeOrders) ?></h2>
                        </div>
                        <div class="bg-primary rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-list fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Danh sách bàn</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Số bàn</th>
                                    <th>Trạng thái</th>
                                    <th>Sức chứa</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tables as $table): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($table['table_number']) ?></td>
                                        <td>
                                            <?php
                                            switch($table['status']) {
                                                case 'available':
                                                    echo '<span class="badge bg-success">Trống</span>';
                                                    break;
                                                case 'occupied':
                                                    echo '<span class="badge bg-danger">Đang sử dụng</span>';
                                                    break;
                                                case 'reserved':
                                                    echo '<span class="badge bg-warning">Đã đặt</span>';
                                                    break;
                                                case 'maintenance':
                                                    echo '<span class="badge bg-secondary">Bảo trì</span>';
                                                    break;
                                            }
                                            ?>
                                        </td>
                                        <td><?= $table['capacity'] ?> người</td>
                                        <td>
                                            <?php if ($table['status'] == 'available'): ?>
                                                <a href="<?= STAFF_URL ?>/orders/create.php?table_id=<?= $table['table_id'] ?>" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-plus"></i> Tạo đơn
                                                </a>
                                            <?php else: ?>
                                                <a href="<?= STAFF_URL ?>/orders/view.php?table_id=<?= $table['table_id'] ?>" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> Xem đơn
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php include __DIR__ . '/../layouts/footer.php'; ?>