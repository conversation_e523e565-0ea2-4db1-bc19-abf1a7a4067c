
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-0">Chào mừng, <?= htmlspecialchars($currentUser['full_name']) ?>!</h1>
            <p class="text-muted">Bảng điều khiển nhân viên - <?= date('d/m/Y') ?></p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="<?= STAFF_URL ?>/orders/create.php" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Tạo đơn hàng mới
                </a>
                <a href="<?= STAFF_URL ?>/tables/index.php" class="btn btn-outline-primary">
                    <i class="fas fa-chair me-1"></i> Q<PERSON><PERSON><PERSON> lý bàn
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Đơn hàng hôm nay</h5>
                            <h2 class="mb-0 fw-bold" id="total-orders-count"><?= count($staffOrders) ?></h2>
                        </div>
                        <div class="bg-primary rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-receipt fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Đơn đang xử lý</h5>
                            <h2 class="mb-0 fw-bold" id="active-orders-count"><?= count($activeOrders) ?></h2>
                        </div>
                        <div class="bg-warning rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-clock fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Doanh thu</h5>
                            <h2 class="mb-0 fw-bold" id="daily-revenue"><?= format_currency($dailyRevenue['total_revenue'] ?? 0) ?></h2>
                        </div>
                        <div class="bg-success rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-dollar-sign fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-sm-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="mb-1">Bàn trống</h5>
                            <h2 class="mb-0 fw-bold" id="available-tables-count"><?= $tableStatus['available'] ?? 0 ?></h2>
                        </div>
                        <div class="bg-info rounded-circle p-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-chair fa-2x text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Kitchen Queue -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-utensils me-2"></i>Hàng đợi bếp
                        <span class="badge bg-dark ms-2" id="kitchen-queue-badge">0</span>
                    </h5>
                    <button class="btn btn-sm btn-outline-dark" id="refresh-kitchen" title="Làm mới">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="card-body p-0">
                    <div id="kitchen-queue-container" style="max-height: 400px; overflow-y: auto;">
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="text-muted mt-2">Đang tải...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Orders -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        Đơn hàng đang xử lý
                        <span class="badge bg-primary ms-2" id="active-orders-badge"><?= count($activeOrders) ?></span>
                    </h5>
                    <div class="d-flex align-items-center">
                        <small class="text-muted me-2" id="last-update">Cập nhật: <?= date('H:i:s') ?></small>
                        <button class="btn btn-sm btn-outline-secondary me-2" id="refresh-orders" title="Làm mới">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <a href="<?= STAFF_URL ?>/orders/index.php" class="btn btn-sm btn-outline-primary">
                            Xem tất cả
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($activeOrders)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Không có đơn hàng đang xử lý</p>
                            <a href="<?= STAFF_URL ?>/orders/create.php" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Tạo đơn hàng mới
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Bàn</th>
                                        <th>Tổng tiền</th>
                                        <th>Trạng thái</th>
                                        <th>Thời gian</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody id="active-orders-tbody">
                                    <?php foreach ($activeOrders as $order): ?>
                                        <?php
                                        // Get table info
                                        $table = $tableModel->getById($order['table_id']);
                                        ?>
                                        <tr data-order-id="<?= $order['order_id'] ?>">
                                            <td><?= $order['order_id'] ?></td>
                                            <td><?= $table['table_number'] ?></td>
                                            <td><?= format_currency($order['final_amount']) ?></td>
                                            <td>
                                                <?php
                                                $statusClass = $order['status'] === 'pending' ? 'bg-warning' : 'bg-info';
                                                $statusText = $order['status'] === 'pending' ? 'Đang chờ' : 'Đang xử lý';
                                                ?>
                                                <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                            </td>
                                            <td><?= date('H:i', strtotime($order['order_date'])) ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= STAFF_URL ?>/orders/view.php?id=<?= $order['order_id'] ?>"
                                                       class="btn btn-outline-primary" title="Xem chi tiết">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= STAFF_URL ?>/orders/edit.php?id=<?= $order['order_id'] ?>"
                                                       class="btn btn-outline-warning" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-outline-success update-status-btn"
                                                            data-order-id="<?= $order['order_id'] ?>"
                                                            data-current-status="<?= $order['status'] ?>"
                                                            title="Cập nhật trạng thái">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Table Status -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Trạng thái bàn</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <?php foreach ($availableTables as $table): ?>
                            <div class="col-6">
                                <div class="card table-card table-available text-center">
                                    <div class="card-body p-2">
                                        <div class="table-number"><?= $table['table_number'] ?></div>
                                        <small class="text-muted"><?= $table['capacity'] ?> chỗ</small>
                                        <div class="mt-1">
                                            <span class="badge bg-success">Trống</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php foreach ($occupiedTables as $table): ?>
                            <div class="col-6">
                                <div class="card table-card table-occupied text-center">
                                    <div class="card-body p-2">
                                        <div class="table-number"><?= $table['table_number'] ?></div>
                                        <small class="text-muted"><?= $table['capacity'] ?> chỗ</small>
                                        <div class="mt-1">
                                            <span class="badge bg-danger">Đang sử dụng</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <a href="<?= STAFF_URL ?>/tables/index.php" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-chair me-1"></i> Quản lý bàn
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Dashboard JavaScript -->
<script>
class RealtimeDashboard {
    constructor() {
        this.updateInterval = 30000; // 30 seconds
        this.intervalId = null;
        this.isUpdating = false;
        this.csrfToken = '<?= csrf_token() ?>';
        this.baseUrl = '<?= BASE_URL ?>';
        this.soundEnabled = localStorage.getItem('dashboard_sound') !== 'false';

        this.init();
    }

    init() {
        this.bindEvents();
        this.startAutoUpdate();
        this.createSoundToggle();
    }

    bindEvents() {
        // Manual refresh buttons
        document.getElementById('refresh-orders')?.addEventListener('click', () => {
            this.updateDashboard();
        });

        document.getElementById('refresh-kitchen')?.addEventListener('click', () => {
            this.updateKitchenQueue();
        });

        // Update status buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.update-status-btn')) {
                this.handleStatusUpdate(e.target.closest('.update-status-btn'));
            }
        });

        // Page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoUpdate();
            } else {
                this.startAutoUpdate();
                this.updateDashboard();
            }
        });
    }

    startAutoUpdate() {
        if (this.intervalId) return;

        this.intervalId = setInterval(() => {
            this.updateDashboard();
        }, this.updateInterval);
    }

    stopAutoUpdate() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    async updateDashboard() {
        if (this.isUpdating) return;

        this.isUpdating = true;
        this.showLoadingState();

        try {
            await Promise.all([
                this.updateActiveOrders(),
                this.updateOrderStats(),
                this.updateTableStatus(),
                this.updateKitchenQueue()
            ]);

            this.updateLastUpdateTime();
        } catch (error) {
            console.error('Dashboard update failed:', error);
            this.showError('Không thể cập nhật dữ liệu');
        } finally {
            this.isUpdating = false;
            this.hideLoadingState();
        }
    }

    async updateActiveOrders() {
        const response = await fetch(`${this.baseUrl}/api/dashboard/realtime.php?action=active_orders`);
        const data = await response.json();

        if (data.success) {
            this.renderActiveOrders(data.orders);
            this.updateOrderCount(data.count);

            // Check for new orders
            this.checkForNewOrders(data.orders);
        }
    }

    async updateOrderStats() {
        const response = await fetch(`${this.baseUrl}/api/dashboard/realtime.php?action=order_stats`);
        const data = await response.json();

        if (data.success) {
            document.getElementById('total-orders-count').textContent = data.stats.total_orders;
            document.getElementById('daily-revenue').textContent = data.stats.total_revenue;
        }
    }

    async updateTableStatus() {
        const response = await fetch(`${this.baseUrl}/api/dashboard/realtime.php?action=table_status`);
        const data = await response.json();

        if (data.success) {
            document.getElementById('available-tables-count').textContent = data.status_counts.available || 0;
        }
    }

    async updateKitchenQueue() {
        const response = await fetch(`${this.baseUrl}/api/dashboard/realtime.php?action=kitchen_queue`);
        const data = await response.json();

        if (data.success) {
            this.renderKitchenQueue(data.queue);
            document.getElementById('kitchen-queue-badge').textContent = data.count;
        }
    }

    renderActiveOrders(orders) {
        const tbody = document.getElementById('active-orders-tbody');
        if (!tbody) return;

        if (orders.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Không có đơn hàng đang xử lý</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = orders.map(order => `
            <tr data-order-id="${order.order_id}">
                <td>${order.order_id}</td>
                <td>${order.table_number}</td>
                <td>${order.formatted_amount}</td>
                <td>
                    <span class="badge ${order.status_class}">${order.status_text}</span>
                    <small class="text-muted d-block">${order.elapsed_time}</small>
                </td>
                <td>${order.order_time}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a href="${this.baseUrl}/staff/orders/view.php?id=${order.order_id}"
                           class="btn btn-outline-primary" title="Xem chi tiết">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="${this.baseUrl}/staff/orders/edit.php?id=${order.order_id}"
                           class="btn btn-outline-warning" title="Chỉnh sửa">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn btn-outline-success update-status-btn"
                                data-order-id="${order.order_id}"
                                data-current-status="${order.status}"
                                title="Cập nhật trạng thái">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    updateOrderCount(count) {
        document.getElementById('active-orders-count').textContent = count;
        document.getElementById('active-orders-badge').textContent = count;
    }

    renderKitchenQueue(queue) {
        const container = document.getElementById('kitchen-queue-container');
        if (!container) return;

        if (queue.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="text-muted">Không có đơn hàng trong hàng đợi</p>
                </div>
            `;
            return;
        }

        container.innerHTML = queue.map((order, index) => {
            const priorityClass = order.priority > 1800 ? 'border-danger' :
                                 order.priority > 900 ? 'border-warning' : 'border-success';
            const priorityIcon = order.priority > 1800 ? 'fas fa-exclamation-triangle text-danger' :
                                order.priority > 900 ? 'fas fa-clock text-warning' : 'fas fa-check text-success';

            return `
                <div class="border-bottom p-3 ${priorityClass} border-start border-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <strong class="me-2">#${order.order_id}</strong>
                                <span class="badge ${order.status_class} me-2">${order.status_text}</span>
                                <small class="text-muted">Bàn ${order.table_number}</small>
                            </div>
                            <div class="d-flex align-items-center text-muted small">
                                <i class="${priorityIcon} me-1"></i>
                                <span class="me-3">${order.elapsed_time}</span>
                                <i class="fas fa-utensils me-1"></i>
                                <span class="me-3">${order.items_count} món</span>
                                <span>${order.total_amount}</span>
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted d-block">${order.order_time}</small>
                            ${index < 3 ? `<span class="badge bg-primary">#${index + 1}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    checkForNewOrders(currentOrders) {
        const currentOrderIds = currentOrders.map(o => o.order_id);
        const previousOrderIds = this.previousOrderIds || [];

        const newOrders = currentOrderIds.filter(id => !previousOrderIds.includes(id));

        if (newOrders.length > 0 && this.previousOrderIds) {
            this.playNotificationSound();
            this.showNotification(`Có ${newOrders.length} đơn hàng mới!`);
        }

        this.previousOrderIds = currentOrderIds;
    }

    async handleStatusUpdate(button) {
        const orderId = button.dataset.orderId;
        const currentStatus = button.dataset.currentStatus;

        let newStatus;
        if (currentStatus === 'pending') {
            newStatus = 'processing';
        } else if (currentStatus === 'processing') {
            newStatus = 'completed';
        } else {
            return;
        }

        try {
            const response = await fetch(`${this.baseUrl}/api/dashboard/realtime.php?action=update_order_status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `order_id=${orderId}&status=${newStatus}&csrf_token=${this.csrfToken}`
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess(data.message);
                this.updateDashboard();
            } else {
                this.showError(data.error || 'Có lỗi xảy ra');
            }
        } catch (error) {
            this.showError('Không thể cập nhật trạng thái đơn hàng');
        }
    }

    playNotificationSound() {
        if (!this.soundEnabled) return;

        // Create audio context for notification sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
    }

    createSoundToggle() {
        const soundToggle = document.createElement('button');
        soundToggle.className = 'btn btn-sm btn-outline-secondary position-fixed';
        soundToggle.style.cssText = 'bottom: 20px; right: 20px; z-index: 1000;';
        soundToggle.innerHTML = `<i class="fas fa-volume-${this.soundEnabled ? 'up' : 'mute'}"></i>`;
        soundToggle.title = this.soundEnabled ? 'Tắt âm thanh' : 'Bật âm thanh';

        soundToggle.addEventListener('click', () => {
            this.soundEnabled = !this.soundEnabled;
            localStorage.setItem('dashboard_sound', this.soundEnabled);
            soundToggle.innerHTML = `<i class="fas fa-volume-${this.soundEnabled ? 'up' : 'mute'}"></i>`;
            soundToggle.title = this.soundEnabled ? 'Tắt âm thanh' : 'Bật âm thanh';
        });

        document.body.appendChild(soundToggle);
    }

    showLoadingState() {
        const refreshBtn = document.getElementById('refresh-orders');
        if (refreshBtn) {
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            refreshBtn.disabled = true;
        }
    }

    hideLoadingState() {
        const refreshBtn = document.getElementById('refresh-orders');
        if (refreshBtn) {
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
            refreshBtn.disabled = false;
        }
    }

    updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('vi-VN', { hour12: false });
        document.getElementById('last-update').textContent = `Cập nhật: ${timeString}`;
    }

    showNotification(message) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = 'toast position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 1050;';
        toast.innerHTML = `
            <div class="toast-header">
                <i class="fas fa-bell text-primary me-2"></i>
                <strong class="me-auto">Thông báo</strong>
                <button type="button" class="btn-close" data-mdb-dismiss="toast"></button>
            </div>
            <div class="toast-body">${message}</div>
        `;

        document.body.appendChild(toast);
        const bsToast = new mdb.Toast(toast);
        bsToast.show();

        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    showError(message) {
        this.showAlert(message, 'danger');
    }

    showAlert(message, type) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 1050; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-mdb-dismiss="alert"></button>
        `;

        document.body.appendChild(alert);

        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
}

// Initialize real-time dashboard
document.addEventListener('DOMContentLoaded', () => {
    new RealtimeDashboard();
});
</script>