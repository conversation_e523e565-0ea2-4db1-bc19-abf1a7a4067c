<?php
/**
 * Helper functions for the application
 */

/**
 * Redirect to a specific URL
 *
 * @param string $url URL to redirect to
 * @return void
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * Get the current URL
 *
 * @return string Current URL
 */
function current_url() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
}

/**
 * Display a success message using session
 *
 * @param string $message Message to display
 * @return void
 */
function set_success_message($message) {
    $_SESSION['success_message'] = $message;
}

/**
 * Display an error message using session
 *
 * @param string $message Message to display
 * @return void
 */
function set_error_message($message) {
    $_SESSION['error_message'] = $message;
}

/**
 * Get and clear success message from session
 *
 * @return string|null Success message or null
 */
function get_success_message() {
    $message = $_SESSION['success_message'] ?? null;
    unset($_SESSION['success_message']);
    return $message;
}

/**
 * Get and clear error message from session
 *
 * @return string|null Error message or null
 */
function get_error_message() {
    $message = $_SESSION['error_message'] ?? null;
    unset($_SESSION['error_message']);
    return $message;
}

/**
 * Format currency to Vietnamese Dong
 *
 * @param float $amount Amount to format
 * @return string Formatted amount
 */
function format_currency($amount) {
    return number_format($amount, 0, ',', '.') . ' ₫';
}

/**
 * Get food item image with fallback
 *
 * @param string|null $imagePath Image path from database
 * @param string $size Size variant (small, medium, large)
 * @return array Array with 'src', 'alt', 'class', 'is_placeholder' keys
 */
function get_food_image($imagePath, $size = 'medium') {
    $sizeClasses = [
        'small' => 'food-img-small',
        'medium' => 'food-img-medium',
        'large' => 'food-img-large'
    ];

    $baseClass = $sizeClasses[$size] ?? $sizeClasses['medium'];

    if (!empty($imagePath) && file_exists('public/' . $imagePath)) {
        return [
            'src' => BASE_URL . '/public/' . $imagePath,
            'alt' => 'Hình ảnh món ăn',
            'class' => $baseClass . ' food-img-real',
            'is_placeholder' => false
        ];
    } else {
        return [
            'src' => 'data:image/svg+xml;base64,' . base64_encode(get_placeholder_svg($size)),
            'alt' => 'Chưa có hình ảnh',
            'class' => $baseClass . ' food-img-placeholder',
            'is_placeholder' => true
        ];
    }
}

/**
 * Generate SVG placeholder for food items
 *
 * @param string $size Size variant
 * @return string SVG content
 */
function get_placeholder_svg($size = 'medium') {
    $dimensions = [
        'small' => ['width' => 50, 'height' => 50, 'icon_size' => 16],
        'medium' => ['width' => 180, 'height' => 180, 'icon_size' => 48],
        'large' => ['width' => 300, 'height' => 300, 'icon_size' => 72]
    ];

    $dim = $dimensions[$size] ?? $dimensions['medium'];
    $iconSize = $dim['icon_size'];
    $iconX = ($dim['width'] - $iconSize) / 2;
    $iconY = ($dim['height'] - $iconSize) / 2;

    return '
    <svg width="' . $dim['width'] . '" height="' . $dim['height'] . '" viewBox="0 0 ' . $dim['width'] . ' ' . $dim['height'] . '" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f8f9fa"/>
        <rect x="0" y="0" width="100%" height="100%" fill="none" stroke="#dee2e6" stroke-width="1"/>
        <g transform="translate(' . $iconX . ',' . $iconY . ')">
            <path d="M' . ($iconSize/2) . ' 0 L' . ($iconSize*0.8) . ' ' . ($iconSize*0.3) . ' L' . $iconSize . ' ' . ($iconSize*0.3) . ' L' . ($iconSize*0.7) . ' ' . ($iconSize*0.5) . ' L' . ($iconSize*0.9) . ' ' . $iconSize . ' L' . ($iconSize*0.1) . ' ' . $iconSize . ' L' . ($iconSize*0.3) . ' ' . ($iconSize*0.5) . ' L0 ' . ($iconSize*0.3) . ' L' . ($iconSize*0.2) . ' ' . ($iconSize*0.3) . ' Z" fill="#6c757d"/>
        </g>
        <text x="50%" y="85%" text-anchor="middle" fill="#6c757d" font-family="Arial, sans-serif" font-size="' . ($iconSize/4) . '">Chưa có ảnh</text>
    </svg>';
}

/**
 * Get current user from session
 *
 * @return array|null User data or null if not logged in
 */
function get_logged_in_user() {
    return $_SESSION['user'] ?? null;
}

/**
 * Check if user is admin
 * 
 * @return bool True if user is admin
 */
function is_admin() {
    $user = get_logged_in_user();
    return $user && $user['role_id'] == 1;
}

/**
 * Check if user is staff
 * 
 * @return bool True if user is staff
 */
function is_staff() {
    $user = get_logged_in_user();
    return $user && $user['role_id'] == 2;
}

/**
 * Check if user is authenticated
 * 
 * @return bool True if user is authenticated
 */
function is_authenticated() {
    return isset($_SESSION['user']);
}

/**
 * Sanitize input data
 * 
 * @param string $data Data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Create CSRF token
 * 
 * @return string CSRF token
 */
function csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * 
 * @param string $token Token to verify
 * @return bool True if token is valid
 */
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Verify permission for a specific role
 * 
 * @param int $required_role_id Required role ID
 * @return bool True if user has permission
 */
function verify_permission($required_role_id) {
    $user = get_logged_in_user();
    if (!$user) {
        redirect(BASE_URL . '/login.php');
        return false;
    }
    
    if ($user['role_id'] != $required_role_id) {
        set_error_message('Bạn không có quyền truy cập trang này');
        redirect(BASE_URL);
        return false;
    }
    
    return true;
}

/**
 * Generate a random string
 * 
 * @param int $length Length of string
 * @return string Random string
 */
function random_string($length = 10) {
    return bin2hex(random_bytes($length));
}