# Food Image Display System

## Overview
This system provides a consistent visual experience for food items throughout the admin and staff interfaces, automatically handling missing images with professional SVG placeholders.

## Features
- **Automatic fallback**: Missing images are replaced with SVG placeholders
- **Multiple sizes**: Small (50x50), Medium (180x180), Large (300x300)
- **Consistent styling**: Unified CSS classes for all image types
- **Responsive design**: Works across all device sizes
- **Professional appearance**: No broken image icons

## Usage

### Basic Implementation
```php
<?php $imageData = get_food_image($food['image_path'], 'medium'); ?>
<img src="<?= $imageData['src'] ?>" 
     alt="<?= htmlspecialchars($food['food_name']) ?>" 
     class="<?= $imageData['class'] ?>">
```

### Size Options
- `'small'` - 50x50px (for table thumbnails)
- `'medium'` - 180x180px (for food cards)
- `'large'` - 300x300px (for image previews)

### Return Values
The `get_food_image()` function returns an array with:
- `'src'` - Image URL or base64 SVG placeholder
- `'alt'` - Appropriate alt text
- `'class'` - CSS classes for styling
- `'is_placeholder'` - Boolean indicating if it's a placeholder

## CSS Classes

### Size Classes
- `.food-img-small` - 50x50px dimensions
- `.food-img-medium` - 180x180px dimensions  
- `.food-img-large` - 300x300px dimensions

### Type Classes
- `.food-img-real` - Real uploaded images (with hover effects)
- `.food-img-placeholder` - SVG placeholders (dashed border)

### Additional Classes
- `.img-thumbnail` - Bootstrap thumbnail styling
- `.card-img-top` - Bootstrap card image styling

## Implementation Examples

### Admin Food List (Table)
```php
<td>
    <?php $imageData = get_food_image($item['image_path'], 'small'); ?>
    <img src="<?= $imageData['src'] ?>" 
         alt="<?= htmlspecialchars($item['food_name']) ?>" 
         class="<?= $imageData['class'] ?> img-thumbnail">
</td>
```

### Staff Order Creation (Cards)
```php
<?php $imageData = get_food_image($food['image_path'], 'medium'); ?>
<img src="<?= $imageData['src'] ?>" 
     class="card-img-top <?= $imageData['class'] ?>" 
     alt="<?= htmlspecialchars($food['food_name']) ?>">
```

### Admin Food Edit (Preview)
```php
<?php $imageData = get_food_image($food['image_path'], 'large'); ?>
<img id="imagePreview" src="<?= $imageData['src'] ?>" 
     alt="<?= htmlspecialchars($food['food_name']) ?>" 
     class="<?= $imageData['class'] ?> mb-3">
```

## JavaScript Integration

### Image Upload Preview
```javascript
imageInput.addEventListener("change", function() {
    if (this.files && this.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            imagePreview.src = e.target.result;
            imagePreview.className = "food-img-large food-img-real mb-3";
        }
        reader.readAsDataURL(this.files[0]);
    } else {
        // Reset to placeholder
        const placeholderSrc = imagePreview.getAttribute("data-placeholder");
        if (placeholderSrc) {
            imagePreview.src = placeholderSrc;
            imagePreview.className = "food-img-large food-img-placeholder mb-3";
        }
    }
});
```

## File Structure
```
app/
├── helpers.php                 # get_food_image() and get_placeholder_svg()
├── views/
│   ├── admin/
│   │   ├── food/
│   │   │   ├── index.php      # Updated with new image system
│   │   │   ├── create.php     # Updated with new image system
│   │   │   └── edit.php       # Updated with new image system
│   │   └── reports/
│   │       └── popular.php    # Updated with new image system
│   └── staff/
│       └── orders/
│           └── create.php     # Updated with new image system
public/
└── css/
    └── style.css              # Image styling classes
```

## Benefits
1. **No broken images**: Eliminates broken image icons
2. **Professional appearance**: Consistent visual design
3. **Better UX**: Clear indication when images are missing
4. **Maintainable**: Centralized image handling logic
5. **Responsive**: Works on all screen sizes
6. **Accessible**: Proper alt text for screen readers

## Migration Guide

### Before (Old Code)
```php
<?php if (!empty($item['image_path'])): ?>
    <img src="<?= BASE_URL ?>/public/<?= $item['image_path'] ?>" 
         alt="<?= htmlspecialchars($item['food_name']) ?>" 
         class="img-thumbnail" style="width: 50px; height: 50px;">
<?php else: ?>
    <img src="<?= BASE_URL ?>/public/images/no-image.jpg" 
         alt="No Image" class="img-thumbnail" 
         style="width: 50px; height: 50px;">
<?php endif; ?>
```

### After (New Code)
```php
<?php $imageData = get_food_image($item['image_path'], 'small'); ?>
<img src="<?= $imageData['src'] ?>" 
     alt="<?= htmlspecialchars($item['food_name']) ?>" 
     class="<?= $imageData['class'] ?> img-thumbnail">
```

## Testing
Visit `/test_food_images.php` to see all image sizes and styles in action.

## Future Enhancements
- Image optimization and compression
- Multiple image formats support
- Lazy loading for better performance
- Image caching mechanisms
- Admin bulk image upload tool
