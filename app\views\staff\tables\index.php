<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-0">Q<PERSON><PERSON><PERSON> lý bàn</h1>
            <p class="text-muted">Theo dõi và quản lý trạng thái các bàn trong nhà hàng</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= STAFF_URL ?>/orders/create.php" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Tạo đơn hàng mới
            </a>
        </div>
    </div>

    <!-- Table Status Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success"><?= $tableStatus['available'] ?? 0 ?></h3>
                    <p class="mb-0">B<PERSON>n trống</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-danger"><?= $tableStatus['occupied'] ?? 0 ?></h3>
                    <p class="mb-0">Đang sử dụng</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning"><?= $tableStatus['reserved'] ?? 0 ?></h3>
                    <p class="mb-0">Đã đặt trước</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-secondary"><?= $tableStatus['maintenance'] ?? 0 ?></h3>
                    <p class="mb-0">Bảo trì</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Grid -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Danh sách bàn</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <?php foreach ($tables as $table): ?>
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="card table-card <?= 'table-' . $table['status'] ?> h-100">
                            <div class="card-body text-center">
                                <div class="table-number"><?= $table['table_number'] ?></div>
                                <div class="table-capacity">
                                    <i class="fas fa-users me-1"></i><?= $table['capacity'] ?> chỗ
                                </div>
                                
                                <div class="mt-2">
                                    <?php
                                    $statusClass = '';
                                    $statusText = '';
                                    
                                    switch ($table['status']) {
                                        case 'available':
                                            $statusClass = 'bg-success';
                                            $statusText = 'Trống';
                                            break;
                                        case 'occupied':
                                            $statusClass = 'bg-danger';
                                            $statusText = 'Đang sử dụng';
                                            break;
                                        case 'reserved':
                                            $statusClass = 'bg-warning';
                                            $statusText = 'Đã đặt';
                                            break;
                                        case 'maintenance':
                                            $statusClass = 'bg-secondary';
                                            $statusText = 'Bảo trì';
                                            break;
                                        default:
                                            $statusClass = 'bg-secondary';
                                            $statusText = 'Không xác định';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?> mb-2"><?= $statusText ?></span>
                                </div>

                                <!-- Order Info for Occupied Tables -->
                                <?php if ($table['status'] === 'occupied' && isset($activeOrders[$table['table_id']])): ?>
                                    <?php $order = $activeOrders[$table['table_id']]; ?>
                                    <div class="mt-2 small text-muted">
                                        <div>Đơn #<?= $order['order_id'] ?></div>
                                        <div><?= date('H:i', strtotime($order['order_date'])) ?></div>
                                        <div><?= format_currency($order['final_amount']) ?></div>
                                    </div>
                                <?php endif; ?>

                                <!-- Action Buttons -->
                                <div class="mt-3">
                                    <?php if ($table['status'] === 'available'): ?>
                                        <a href="<?= STAFF_URL ?>/orders/create.php?table_id=<?= $table['table_id'] ?>" 
                                           class="btn btn-primary btn-sm w-100 mb-1">
                                            <i class="fas fa-plus"></i> Tạo đơn
                                        </a>
                                        <div class="btn-group w-100">
                                            <button type="button" class="btn btn-outline-warning btn-sm" 
                                                    onclick="setTableStatus(<?= $table['table_id'] ?>, 'reserved')">
                                                <i class="fas fa-bookmark"></i> Đặt trước
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                    onclick="setTableStatus(<?= $table['table_id'] ?>, 'maintenance')">
                                                <i class="fas fa-tools"></i> Bảo trì
                                            </button>
                                        </div>
                                    <?php elseif ($table['status'] === 'occupied'): ?>
                                        <?php if (isset($activeOrders[$table['table_id']])): ?>
                                            <a href="<?= STAFF_URL ?>/orders/view.php?id=<?= $activeOrders[$table['table_id']]['order_id'] ?>" 
                                               class="btn btn-info btn-sm w-100 mb-1">
                                                <i class="fas fa-eye"></i> Xem đơn
                                            </a>
                                            <a href="<?= STAFF_URL ?>/orders/edit.php?id=<?= $activeOrders[$table['table_id']]['order_id'] ?>" 
                                               class="btn btn-warning btn-sm w-100">
                                                <i class="fas fa-edit"></i> Chỉnh sửa
                                            </a>
                                        <?php endif; ?>
                                    <?php elseif ($table['status'] === 'reserved'): ?>
                                        <a href="<?= STAFF_URL ?>/orders/create.php?table_id=<?= $table['table_id'] ?>" 
                                           class="btn btn-primary btn-sm w-100 mb-1">
                                            <i class="fas fa-plus"></i> Tạo đơn
                                        </a>
                                        <button type="button" class="btn btn-outline-success btn-sm w-100" 
                                                onclick="setTableStatus(<?= $table['table_id'] ?>, 'available')">
                                            <i class="fas fa-check"></i> Hủy đặt trước
                                        </button>
                                    <?php elseif ($table['status'] === 'maintenance'): ?>
                                        <button type="button" class="btn btn-success btn-sm w-100" 
                                                onclick="setTableStatus(<?= $table['table_id'] ?>, 'available')">
                                            <i class="fas fa-check"></i> Hoàn thành bảo trì
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for table status updates -->
<form id="tableStatusForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
    <input type="hidden" name="action" id="statusAction">
    <input type="hidden" name="table_id" id="statusTableId">
</form>

<script>
function setTableStatus(tableId, status) {
    let actionText = '';
    let confirmText = '';
    
    switch (status) {
        case 'available':
            actionText = 'set_available';
            confirmText = 'Bạn có chắc chắn muốn chuyển bàn này sang trạng thái trống?';
            break;
        case 'reserved':
            actionText = 'set_reserved';
            confirmText = 'Bạn có chắc chắn muốn đặt trước bàn này?';
            break;
        case 'maintenance':
            actionText = 'set_maintenance';
            confirmText = 'Bạn có chắc chắn muốn chuyển bàn này sang trạng thái bảo trì?';
            break;
        default:
            return;
    }
    
    if (confirm(confirmText)) {
        document.getElementById('statusAction').value = actionText;
        document.getElementById('statusTableId').value = tableId;
        document.getElementById('tableStatusForm').submit();
    }
}

// Auto refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>
