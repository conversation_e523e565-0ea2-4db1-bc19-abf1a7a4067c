<?php
/**
 * Staff Dashboard
 */
require_once '../app/config/config.php';
require_once '../app/config/database.php';
require_once '../app/helpers.php';
require_once '../app/models/User.php';
require_once '../app/models/Table.php';
require_once '../app/models/Order.php';
require_once '../app/models/FoodItem.php';

// Check if user is staff
if (!is_staff()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$tableModel = new Table($conn);
$orderModel = new Order($conn);
$foodModel = new FoodItem($conn);

// Get current user
$currentUser = get_logged_in_user();

// Get table status counts
$tableStatus = $tableModel->getStatusCounts();

// Get today's orders for this staff member
$today = date('Y-m-d');
$staffOrders = $orderModel->findBy(
    'user_id = :user_id AND DATE(order_date) = :today',
    [':user_id' => $currentUser['user_id'], ':today' => $today],
    'order_date DESC'
);

// Get active orders for this staff member
$activeOrders = $orderModel->findBy(
    'user_id = :user_id AND status IN (:status1, :status2)',
    [
        ':user_id' => $currentUser['user_id'],
        ':status1' => 'pending',
        ':status2' => 'processing'
    ],
    'order_date DESC'
);

// Get today's revenue for this staff member
$todayRevenue = $orderModel->query(
    "SELECT SUM(p.payment_amount) as total_revenue, COUNT(p.payment_id) as order_count 
     FROM payments p 
     JOIN orders o ON p.order_id = o.order_id 
     WHERE o.user_id = :user_id AND DATE(p.payment_date) = :today AND p.payment_status = 'completed'",
    [':user_id' => $currentUser['user_id'], ':today' => $today]
);

$dailyRevenue = $todayRevenue[0] ?? ['total_revenue' => 0, 'order_count' => 0];

// Get available tables
$availableTables = $tableModel->findBy('status = :status', [':status' => 'available'], 'table_number ASC');

// Get occupied tables
$occupiedTables = $tableModel->findBy('status = :status', [':status' => 'occupied'], 'table_number ASC');

// Set page title
$pageTitle = 'Bảng điều khiển nhân viên';

// Include content in layout
$content = VIEWS_DIR . '/staff/dashboard.php';
include_once VIEWS_DIR . '/layouts/main.php';
