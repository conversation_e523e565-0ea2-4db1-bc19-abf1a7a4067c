/* Main Styling */
body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f5f5f5;
}

.main-content {
    flex: 1 0 auto;
    padding: 20px 0;
}

/* Dashboard styling */
.dashboard-icon {
    font-size: 2rem;
    color: #4f4f4f;
}

.table-capacity {
    display: inline-block;
    margin-top: 5px;
}

/* Food item styling */
.food-item-card {
    transition: transform 0.3s;
}

.food-item-card:hover {
    transform: translateY(-5px);
}

.food-item-img {
    height: 180px;
    object-fit: cover;
}

/* Food Image Sizes */
.food-img-small {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
}

.food-img-medium {
    width: 180px;
    height: 180px;
    object-fit: cover;
    border-radius: 8px;
}

.food-img-large {
    width: 300px;
    height: 300px;
    object-fit: cover;
    border-radius: 12px;
}

/* Food Image Types */
.food-img-real {
    border: 1px solid #dee2e6;
    transition: transform 0.2s ease;
}

.food-img-real:hover {
    transform: scale(1.02);
}

.food-img-placeholder {
    border: 2px dashed #dee2e6;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: border-color 0.2s ease;
}

.food-img-placeholder:hover {
    border-color: #6c757d;
}

/* Specific overrides for card images */
.card-img-top.food-img-medium,
.card-img-top.food-img-placeholder {
    border-radius: 0.375rem 0.375rem 0 0;
    border-bottom: none;
}

.food-item-price {
    font-weight: bold;
    color: #1565c0;
}

/* Enhanced Real-time Dashboard Styles */
.dashboard-card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.table-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s;
    cursor: pointer;
}

.table-card:hover {
    transform: scale(1.03);
}

.table-available {
    background-color: #e3f2fd;
    border: 2px solid #2196f3;
}

.table-occupied {
    background-color: #ffebee;
    border: 2px solid #f44336;
}

.table-reserved {
    background-color: #fff8e1;
    border: 2px solid #ffc107;
}

.table-maintenance {
    background-color: #e0e0e0;
    border: 2px solid #9e9e9e;
}

.table-number {
    font-size: 1.4rem;
    font-weight: bold;
}

/* Kitchen Queue Styles */
.kitchen-queue-item {
    transition: background-color 0.2s ease;
}

.kitchen-queue-item:hover {
    background-color: #f8f9fa;
}

.priority-high {
    border-left: 4px solid #dc3545 !important;
    background-color: #fff5f5;
}

.priority-medium {
    border-left: 4px solid #ffc107 !important;
    background-color: #fffbf0;
}

.priority-low {
    border-left: 4px solid #28a745 !important;
    background-color: #f0fff4;
}

/* Real-time Updates */
.updating {
    opacity: 0.7;
    pointer-events: none;
}

.new-order {
    animation: newOrderPulse 2s ease-in-out;
}

@keyframes newOrderPulse {
    0% { background-color: #fff3cd; }
    50% { background-color: #ffeaa7; }
    100% { background-color: transparent; }
}

.status-updated {
    animation: statusUpdateFlash 1s ease-in-out;
}

@keyframes statusUpdateFlash {
    0% { background-color: #d1ecf1; }
    50% { background-color: #bee5eb; }
    100% { background-color: transparent; }
}

/* Sound Toggle Button */
.sound-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .kitchen-queue-container {
        max-height: 300px;
    }

    .dashboard-card {
        margin-bottom: 1rem;
    }

    .table-card {
        margin-bottom: 0.5rem;
    }
}

/* Form styling */
.form-outline {
    margin-bottom: 1.5rem;
}

.required-field::after {
    content: ' *';
    color: #f44336;
}

/* Login page */
.login-container {
    min-height: calc(100vh - 100px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    max-width: 400px;
    width: 100%;
}

/* Order page styling */
.order-summary {
    position: sticky;
    top: 20px;
}

/* Reports */
.chart-container {
    height: 400px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-card {
        margin-bottom: 15px;
    }
    
    .food-item-card {
        margin-bottom: 20px;
    }
    
    .chart-container {
        height: 300px;
    }
}