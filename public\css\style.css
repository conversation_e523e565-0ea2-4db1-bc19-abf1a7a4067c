/* Main Styling */
body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f5f5f5;
}

.main-content {
    flex: 1 0 auto;
    padding: 20px 0;
}

/* Dashboard styling */
.dashboard-card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.dashboard-icon {
    font-size: 2rem;
    color: #4f4f4f;
}

/* Table management */
.table-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s;
}

.table-card:hover {
    transform: scale(1.03);
}

.table-available {
    background-color: #e3f2fd;
    border: 2px solid #2196f3;
}

.table-occupied {
    background-color: #ffebee;
    border: 2px solid #f44336;
}

.table-reserved {
    background-color: #fff8e1;
    border: 2px solid #ffc107;
}

.table-maintenance {
    background-color: #e0e0e0;
    border: 2px solid #9e9e9e;
}

.table-number {
    font-size: 1.4rem;
    font-weight: bold;
}

.table-capacity {
    display: inline-block;
    margin-top: 5px;
}

/* Food item styling */
.food-item-card {
    transition: transform 0.3s;
}

.food-item-card:hover {
    transform: translateY(-5px);
}

.food-item-img {
    height: 180px;
    object-fit: cover;
}

/* Food Image Sizes */
.food-img-small {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
}

.food-img-medium {
    width: 180px;
    height: 180px;
    object-fit: cover;
    border-radius: 8px;
}

.food-img-large {
    width: 300px;
    height: 300px;
    object-fit: cover;
    border-radius: 12px;
}

/* Food Image Types */
.food-img-real {
    border: 1px solid #dee2e6;
    transition: transform 0.2s ease;
}

.food-img-real:hover {
    transform: scale(1.02);
}

.food-img-placeholder {
    border: 2px dashed #dee2e6;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: border-color 0.2s ease;
}

.food-img-placeholder:hover {
    border-color: #6c757d;
}

/* Specific overrides for card images */
.card-img-top.food-img-medium,
.card-img-top.food-img-placeholder {
    border-radius: 0.375rem 0.375rem 0 0;
    border-bottom: none;
}

.food-item-price {
    font-weight: bold;
    color: #1565c0;
}

/* Form styling */
.form-outline {
    margin-bottom: 1.5rem;
}

.required-field::after {
    content: ' *';
    color: #f44336;
}

/* Login page */
.login-container {
    min-height: calc(100vh - 100px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    max-width: 400px;
    width: 100%;
}

/* Order page styling */
.order-summary {
    position: sticky;
    top: 20px;
}

/* Reports */
.chart-container {
    height: 400px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-card {
        margin-bottom: 15px;
    }
    
    .food-item-card {
        margin-bottom: 20px;
    }
    
    .chart-container {
        height: 300px;
    }
}