# Real-time Restaurant Management System - Priority 1

## Overview
This document describes the implementation of **Priority 1: Real-time Order Management System** for the Vietnamese restaurant application. The system provides AJAX-based real-time updates for order status changes across all staff interfaces.

## Features Implemented

### 🔄 Real-time Dashboard Updates
- **Auto-refresh every 30 seconds** for all dashboard components
- **Manual refresh buttons** for immediate updates
- **Live order status tracking** with elapsed time display
- **Kitchen queue visualization** with priority indicators
- **Table status monitoring** with real-time availability

### 🔊 Sound Notifications
- **Audio alerts** for new orders and status changes
- **Configurable sound toggle** with localStorage persistence
- **Web Audio API** for cross-browser compatibility
- **Visual notifications** with toast messages

### 📊 Live Statistics
- **Real-time order counts** (total, active, completed)
- **Daily revenue tracking** with automatic updates
- **Table availability monitoring**
- **Kitchen queue management** with priority sorting

## File Structure

```
api/
└── dashboard/
    └── realtime.php          # Main API endpoint for real-time data

staff/
└── dashboard.php             # Enhanced with real-time functionality

app/
├── views/
│   └── staff/
│       └── dashboard.php     # Updated view with JavaScript
└── models/
    ├── Order.php             # Uses existing getActiveOrdersByUser()
    └── Table.php             # Uses existing getStatusCounts()

public/
└── css/
    └── style.css             # Enhanced with real-time styles
```

## API Endpoints

### Base URL: `/api/dashboard/realtime.php`

#### 1. Active Orders
```
GET ?action=active_orders
```
**Response:**
```json
{
  "success": true,
  "orders": [
    {
      "order_id": 123,
      "table_number": "B01",
      "total_amount": 150000,
      "status": "pending",
      "status_text": "Đang chờ",
      "status_class": "bg-warning",
      "order_time": "14:30",
      "elapsed_time": "15m",
      "formatted_amount": "150.000 ₫"
    }
  ],
  "count": 5,
  "timestamp": 1703123456
}
```

#### 2. Table Status
```
GET ?action=table_status
```
**Response:**
```json
{
  "success": true,
  "status_counts": {
    "available": 8,
    "occupied": 4,
    "reserved": 2,
    "maintenance": 1
  },
  "tables": [...],
  "timestamp": 1703123456
}
```

#### 3. Kitchen Queue
```
GET ?action=kitchen_queue
```
**Response:**
```json
{
  "success": true,
  "queue": [
    {
      "order_id": 123,
      "table_number": "B01",
      "status": "pending",
      "priority": 1800,
      "elapsed_time": "30m",
      "items_count": 3
    }
  ],
  "count": 5,
  "timestamp": 1703123456
}
```

#### 4. Order Statistics
```
GET ?action=order_stats
```
**Response:**
```json
{
  "success": true,
  "stats": {
    "total_orders": 15,
    "completed_orders": 10,
    "pending_orders": 5,
    "total_revenue": "2.500.000 ₫",
    "raw_revenue": 2500000
  },
  "timestamp": 1703123456
}
```

#### 5. Update Order Status
```
POST ?action=update_order_status
Body: order_id=123&status=processing&csrf_token=...
```
**Response:**
```json
{
  "success": true,
  "message": "Trạng thái đơn hàng đã được cập nhật",
  "new_status": "processing",
  "status_text": "Đang xử lý",
  "status_class": "bg-info"
}
```

## JavaScript Implementation

### RealtimeDashboard Class
```javascript
class RealtimeDashboard {
    constructor() {
        this.updateInterval = 30000; // 30 seconds
        this.soundEnabled = localStorage.getItem('dashboard_sound') !== 'false';
        this.init();
    }
    
    // Core methods:
    // - updateDashboard()
    // - updateActiveOrders()
    // - updateKitchenQueue()
    // - handleStatusUpdate()
    // - playNotificationSound()
}
```

### Key Features
- **Automatic updates** every 30 seconds
- **Page visibility detection** (stops when tab is hidden)
- **Sound notifications** with user control
- **Error handling** with user-friendly messages
- **Loading states** with visual feedback

## CSS Enhancements

### Real-time Styles
```css
/* Dashboard animations */
.new-order {
    animation: newOrderPulse 2s ease-in-out;
}

.status-updated {
    animation: statusUpdateFlash 1s ease-in-out;
}

/* Kitchen queue priority indicators */
.priority-high { border-left: 4px solid #dc3545; }
.priority-medium { border-left: 4px solid #ffc107; }
.priority-low { border-left: 4px solid #28a745; }
```

## Security Features

### CSRF Protection
- All POST requests require valid CSRF tokens
- Token verification using existing `verify_csrf_token()` function

### Authentication
- API endpoints check user authentication
- Role-based access control (staff/admin only)
- User ownership validation for order updates

### Input Validation
- Sanitized input using existing `sanitize()` function
- Whitelisted status values
- Numeric validation for IDs

## Integration with Existing System

### Models Used
- `Order::getActiveOrdersByUser()` - Get user's active orders
- `Order::getActiveOrders()` - Get all active orders for kitchen
- `Table::getStatusCounts()` - Get table status statistics
- `Table::getAllTablesWithOrders()` - Get tables with order info

### Helper Functions
- `format_currency()` - Vietnamese currency formatting
- `csrf_token()` - CSRF token generation
- `verify_csrf_token()` - CSRF token validation
- `is_staff()` / `is_admin()` - Role checking

### Localization
- All text in Vietnamese using existing patterns
- Date formatting: `dd/mm/yyyy`
- Time formatting: `H:i` (24-hour format)
- Currency formatting: `number.format ₫`

## Performance Considerations

### Optimizations
- **Conditional updates** - Only update changed data
- **Efficient queries** - Reuse existing optimized model methods
- **Client-side caching** - Store previous state for comparison
- **Debounced requests** - Prevent multiple simultaneous updates

### Browser Compatibility
- **Modern browsers** with Fetch API support
- **Fallback handling** for older browsers
- **Progressive enhancement** - Works without JavaScript

## Testing

### Test Page
Visit `/test_realtime_api.php` to:
- Test all API endpoints individually
- Verify auto-refresh functionality
- Check error handling
- Monitor performance

### Manual Testing
1. **Open staff dashboard** in multiple browser tabs
2. **Create new orders** in one tab
3. **Verify real-time updates** in other tabs
4. **Test sound notifications**
5. **Check mobile responsiveness**

## Future Enhancements (Next Priorities)

### Priority 2: Advanced Reporting
- Chart.js integration for visual analytics
- PDF export functionality
- Enhanced filtering options

### Priority 3: Kitchen Management
- Dedicated kitchen interface
- Order preparation time tracking
- Inventory alerts

### Priority 4: Customer Features
- QR code menu system
- Table-side ordering
- Customer feedback integration

## Troubleshooting

### Common Issues
1. **No updates showing**: Check browser console for JavaScript errors
2. **Sound not working**: Verify user interaction (required for Web Audio API)
3. **API errors**: Check server logs and database connectivity
4. **CSRF failures**: Ensure proper token handling in forms

### Debug Mode
Enable debug mode by adding `?debug=1` to API URLs for detailed error information.

## Maintenance

### Regular Tasks
- Monitor API response times
- Check error logs for failed requests
- Update sound notification preferences
- Optimize database queries as needed

### Monitoring
- Track API endpoint usage
- Monitor real-time update frequency
- Check browser compatibility issues
- Analyze user interaction patterns
