
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-0">Chi tiết đơn hàng #<?= $order['order_id'] ?></h1>
            <p class="text-muted">Bàn <?= $order['table_number'] ?> • <?= date('d/m/Y H:i', strtotime($order['order_date'])) ?></p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= STAFF_URL ?>/orders/index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Quay lại danh sách
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Order Information -->
        <div class="col-lg-8 mb-4">
            <!-- Order Details -->
            <div class="card mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Thông tin đơn hàng</h5>
                    <?php
                    $statusClass = '';
                    $statusText = '';

                    switch ($order['status']) {
                        case 'pending':
                            $statusClass = 'bg-warning';
                            $statusText = 'Đang chờ';
                            break;
                        case 'processing':
                            $statusClass = 'bg-info';
                            $statusText = 'Đang xử lý';
                            break;
                        case 'completed':
                            $statusClass = 'bg-success';
                            $statusText = 'Hoàn thành';
                            break;
                        case 'cancelled':
                            $statusClass = 'bg-danger';
                            $statusText = 'Đã hủy';
                            break;
                        default:
                            $statusClass = 'bg-secondary';
                            $statusText = 'Không xác định';
                            break;
                    }
                    ?>
                    <span class="badge <?= $statusClass ?> fs-6"><?= $statusText ?></span>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Mã đơn hàng:</strong></div>
                        <div class="col-sm-9">#<?= $order['order_id'] ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Bàn:</strong></div>
                        <div class="col-sm-9"><?= $order['table_number'] ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Nhân viên:</strong></div>
                        <div class="col-sm-9"><?= $order['staff_name'] ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Thời gian tạo:</strong></div>
                        <div class="col-sm-9"><?= date('d/m/Y H:i:s', strtotime($order['order_date'])) ?></div>
                    </div>
                    <?php if ($order['notes']): ?>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Ghi chú:</strong></div>
                            <div class="col-sm-9"><?= htmlspecialchars($order['notes']) ?></div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Chi tiết món ăn</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Món ăn</th>
                                    <th>Số lượng</th>
                                    <th>Đơn giá</th>
                                    <th>Thành tiền</th>
                                    <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                        <th>Ghi chú</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($order['details'] as $detail): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($detail['food_name']) ?></td>
                                        <td><?= $detail['quantity'] ?></td>
                                        <td><?= format_currency($detail['unit_price']) ?></td>
                                        <td><?= format_currency($detail['subtotal']) ?></td>
                                        <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                            <td>
                                                <?php if ($detail['notes']): ?>
                                                    <small class="text-muted"><?= htmlspecialchars($detail['notes']) ?></small>
                                                <?php else: ?>
                                                    <small class="text-muted">-</small>
                                                <?php endif; ?>
                                            </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Tổng cộng:</th>
                                    <th><?= format_currency($order['total_amount']) ?></th>
                                    <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                        <th></th>
                                    <?php endif; ?>
                                </tr>
                                <?php if ($order['discount_percent'] > 0): ?>
                                    <tr>
                                        <th colspan="3">Giảm giá (<?= $order['discount_percent'] ?>%):</th>
                                        <th class="text-success">-<?= format_currency($order['discount_amount']) ?></th>
                                        <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                            <th></th>
                                        <?php endif; ?>
                                    </tr>
                                <?php endif; ?>
                                <tr class="table-success">
                                    <th colspan="3">Thành tiền:</th>
                                    <th><?= format_currency($order['final_amount']) ?></th>
                                    <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                        <th></th>
                                    <?php endif; ?>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <?php if ($hasPayment): ?>
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Thông tin thanh toán</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Số tiền:</strong></div>
                            <div class="col-sm-9"><?= format_currency($paymentInfo['payment_amount']) ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Phương thức:</strong></div>
                            <div class="col-sm-9">
                                <?php
                                $methodText = '';
                                switch ($paymentInfo['payment_method']) {
                                    case 'cash':
                                        $methodText = 'Tiền mặt';
                                        break;
                                    case 'credit_card':
                                        $methodText = 'Thẻ tín dụng';
                                        break;
                                    case 'debit_card':
                                        $methodText = 'Thẻ ghi nợ';
                                        break;
                                    case 'mobile_payment':
                                        $methodText = 'Thanh toán di động';
                                        break;
                                    default:
                                        $methodText = 'Khác';
                                        break;
                                }
                                echo $methodText;
                                ?>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Thời gian:</strong></div>
                            <div class="col-sm-9"><?= date('d/m/Y H:i:s', strtotime($paymentInfo['payment_date'])) ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Trạng thái:</strong></div>
                            <div class="col-sm-9">
                                <?php
                                $paymentStatusClass = '';
                                $paymentStatusText = '';

                                switch ($paymentInfo['payment_status']) {
                                    case 'completed':
                                        $paymentStatusClass = 'bg-success';
                                        $paymentStatusText = 'Thành công';
                                        break;
                                    case 'pending':
                                        $paymentStatusClass = 'bg-warning';
                                        $paymentStatusText = 'Đang xử lý';
                                        break;
                                    case 'failed':
                                        $paymentStatusClass = 'bg-danger';
                                        $paymentStatusText = 'Thất bại';
                                        break;
                                    case 'refunded':
                                        $paymentStatusClass = 'bg-info';
                                        $paymentStatusText = 'Đã hoàn';
                                        break;
                                    default:
                                        $paymentStatusClass = 'bg-secondary';
                                        $paymentStatusText = 'Không xác định';
                                        break;
                                }
                                ?>
                                <span class="badge <?= $paymentStatusClass ?>"><?= $paymentStatusText ?></span>
                            </div>
                        </div>
                        <?php if ($paymentInfo['notes']): ?>
                            <div class="row mb-3">
                                <div class="col-sm-3"><strong>Ghi chú:</strong></div>
                                <div class="col-sm-9"><?= htmlspecialchars($paymentInfo['notes']) ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Actions Panel -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thao tác</h5>
                </div>
                <div class="card-body">
                    <?php if ($order['status'] === 'pending'): ?>
                        <form method="POST" class="mb-3">
                            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                            <input type="hidden" name="action" value="start_processing">
                            <button type="submit" class="btn btn-info w-100">
                                <i class="fas fa-play me-1"></i> Bắt đầu xử lý
                            </button>
                        </form>
                    <?php endif; ?>

                    <?php if ($order['status'] === 'processing'): ?>
                        <form method="POST" class="mb-3">
                            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                            <input type="hidden" name="action" value="complete_order">
                            <button type="submit" class="btn btn-success w-100"
                                    onclick="return confirm('Xác nhận hoàn thành đơn hàng?')">
                                <i class="fas fa-check me-1"></i> Hoàn thành đơn hàng
                            </button>
                        </form>
                    <?php endif; ?>

                    <?php if (in_array($order['status'], ['pending', 'processing'])): ?>
                        <a href="<?= STAFF_URL ?>/orders/edit.php?id=<?= $order['order_id'] ?>"
                           class="btn btn-warning w-100 mb-3">
                            <i class="fas fa-edit me-1"></i> Chỉnh sửa đơn hàng
                        </a>

                        <form method="POST" class="mb-3">
                            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                            <input type="hidden" name="action" value="cancel_order">
                            <button type="submit" class="btn btn-danger w-100"
                                    onclick="return confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')">
                                <i class="fas fa-times me-1"></i> Hủy đơn hàng
                            </button>
                        </form>
                    <?php endif; ?>

                    <?php if ($order['status'] === 'completed' && !$hasPayment): ?>
                        <a href="<?= STAFF_URL ?>/payments/create.php?order_id=<?= $order['order_id'] ?>"
                           class="btn btn-success w-100 mb-3">
                            <i class="fas fa-credit-card me-1"></i> Xử lý thanh toán
                        </a>
                    <?php endif; ?>

                    <hr>

                    <div class="d-grid gap-2">
                        <a href="<?= STAFF_URL ?>/orders/index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-1"></i> Danh sách đơn hàng
                        </a>
                        <a href="<?= STAFF_URL ?>/dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-tachometer-alt me-1"></i> Bảng điều khiển
                        </a>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">Tóm tắt đơn hàng</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tổng cộng:</span>
                        <span><?= format_currency($order['total_amount']) ?></span>
                    </div>
                    <?php if ($order['discount_percent'] > 0): ?>
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>Giảm giá (<?= $order['discount_percent'] ?>%):</span>
                            <span>-<?= format_currency($order['discount_amount']) ?></span>
                        </div>
                    <?php endif; ?>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold">
                        <span>Thành tiền:</span>
                        <span class="text-success"><?= format_currency($order['final_amount']) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>