
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-0">Tạo đơn hàng mới</h1>
            <p class="text-muted">Chọn bàn và món ăn để tạo đơn hàng</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= STAFF_URL ?>/orders/index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Quay lại danh sách
            </a>
        </div>
    </div>

    <form method="POST" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

        <div class="row">
            <!-- Order Form -->
            <div class="col-lg-8">
                <!-- Table Selection -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Chọn bàn</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <?php if (empty($availableTables)): ?>
                                <div class="col-12">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Hiện tại không có bàn trống nào. Vui lòng kiểm tra lại sau.
                                    </div>
                                </div>
                            <?php else: ?>
                                <?php foreach ($availableTables as $table): ?>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="table_id"
                                                   id="table_<?= $table['table_id'] ?>" value="<?= $table['table_id'] ?>"
                                                   <?= $selectedTableId == $table['table_id'] ? 'checked' : '' ?> required>
                                            <label class="form-check-label w-100" for="table_<?= $table['table_id'] ?>">
                                                <div class="card table-card table-available">
                                                    <div class="card-body text-center p-2">
                                                        <div class="table-number"><?= $table['table_number'] ?></div>
                                                        <small class="text-muted"><?= $table['capacity'] ?> chỗ</small>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Food Selection -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Chọn món ăn</h5>
                    </div>
                    <div class="card-body">
                        <!-- Category Tabs -->
                        <ul class="nav nav-tabs mb-3" id="categoryTabs" role="tablist">
                            <?php foreach ($categories as $index => $category): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link <?= $index === 0 ? 'active' : '' ?>"
                                            id="category-<?= $category['category_id'] ?>-tab"
                                            data-mdb-toggle="tab"
                                            data-mdb-target="#category-<?= $category['category_id'] ?>"
                                            type="button" role="tab">
                                        <?= htmlspecialchars($category['category_name']) ?>
                                    </button>
                                </li>
                            <?php endforeach; ?>
                        </ul>

                        <!-- Category Content -->
                        <div class="tab-content" id="categoryTabsContent">
                            <?php foreach ($categories as $index => $category): ?>
                                <div class="tab-pane fade <?= $index === 0 ? 'show active' : '' ?>"
                                     id="category-<?= $category['category_id'] ?>" role="tabpanel">
                                    <div class="row g-3">
                                        <?php if (isset($foodByCategory[$category['category_id']])): ?>
                                            <?php foreach ($foodByCategory[$category['category_id']] as $food): ?>
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="card food-item-card h-100">
                                                        <?php if ($food['image_path']): ?>
                                                            <img src="<?= UPLOAD_PATH ?>/<?= $food['image_path'] ?>"
                                                                 class="card-img-top food-item-img"
                                                                 alt="<?= htmlspecialchars($food['food_name']) ?>">
                                                        <?php else: ?>
                                                            <div class="card-img-top food-item-img bg-light d-flex align-items-center justify-content-center">
                                                                <i class="fas fa-utensils fa-2x text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div class="card-body">
                                                            <h6 class="card-title"><?= htmlspecialchars($food['food_name']) ?></h6>
                                                            <p class="card-text small text-muted"><?= htmlspecialchars($food['description']) ?></p>
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <span class="food-item-price"><?= format_currency($food['price']) ?></span>
                                                                <div class="btn-group btn-group-sm">
                                                                    <button type="button" class="btn btn-outline-primary add-to-order"
                                                                            data-food-id="<?= $food['food_id'] ?>"
                                                                            data-food-name="<?= htmlspecialchars($food['food_name']) ?>"
                                                                            data-food-price="<?= $food['price'] ?>">
                                                                        <i class="fas fa-plus"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="col-12">
                                                <p class="text-muted text-center">Không có món ăn nào trong danh mục này.</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Order Notes -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Ghi chú đơn hàng</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-outline">
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            <label class="form-label" for="notes">Ghi chú (tùy chọn)</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card order-summary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Tóm tắt đơn hàng</h5>
                    </div>
                    <div class="card-body">
                        <div id="order-items">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                <p>Chưa có món ăn nào được chọn</p>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between">
                            <strong>Tổng cộng:</strong>
                            <strong id="order-total">0 ₫</strong>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-success w-100" id="submit-order" disabled>
                            <i class="fas fa-check me-1"></i> Tạo đơn hàng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const orderItems = new Map();
    const orderItemsContainer = document.getElementById('order-items');
    const orderTotal = document.getElementById('order-total');
    const submitButton = document.getElementById('submit-order');

    // Add to order functionality
    document.querySelectorAll('.add-to-order').forEach(button => {
        button.addEventListener('click', function() {
            const foodId = this.dataset.foodId;
            const foodName = this.dataset.foodName;
            const foodPrice = parseFloat(this.dataset.foodPrice);

            if (orderItems.has(foodId)) {
                orderItems.get(foodId).quantity++;
            } else {
                orderItems.set(foodId, {
                    id: foodId,
                    name: foodName,
                    price: foodPrice,
                    quantity: 1
                });
            }

            updateOrderSummary();
        });
    });

    function updateOrderSummary() {
        if (orderItems.size === 0) {
            orderItemsContainer.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <p>Chưa có món ăn nào được chọn</p>
                </div>
            `;
            orderTotal.textContent = '0 ₫';
            submitButton.disabled = true;
            return;
        }

        let html = '';
        let total = 0;

        orderItems.forEach(item => {
            const subtotal = item.price * item.quantity;
            total += subtotal;

            html += `
                <div class="d-flex justify-content-between align-items-center mb-2 order-item" data-food-id="${item.id}">
                    <div class="flex-grow-1">
                        <div class="fw-bold">${item.name}</div>
                        <small class="text-muted">${formatCurrency(item.price)} x ${item.quantity}</small>
                        <input type="hidden" name="items[${item.id}][food_id]" value="${item.id}">
                        <input type="hidden" name="items[${item.id}][quantity]" value="${item.quantity}">
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="btn-group btn-group-sm me-2">
                            <button type="button" class="btn btn-outline-secondary quantity-decrease" data-food-id="${item.id}">-</button>
                            <span class="btn btn-outline-secondary">${item.quantity}</span>
                            <button type="button" class="btn btn-outline-secondary quantity-increase" data-food-id="${item.id}">+</button>
                        </div>
                        <button type="button" class="btn btn-outline-danger btn-sm remove-item" data-food-id="${item.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        orderItemsContainer.innerHTML = html;
        orderTotal.textContent = formatCurrency(total);
        submitButton.disabled = false;

        // Add event listeners for quantity controls
        document.querySelectorAll('.quantity-decrease').forEach(btn => {
            btn.addEventListener('click', function() {
                const foodId = this.dataset.foodId;
                if (orderItems.has(foodId)) {
                    const item = orderItems.get(foodId);
                    if (item.quantity > 1) {
                        item.quantity--;
                        updateOrderSummary();
                    }
                }
            });
        });

        document.querySelectorAll('.quantity-increase').forEach(btn => {
            btn.addEventListener('click', function() {
                const foodId = this.dataset.foodId;
                if (orderItems.has(foodId)) {
                    orderItems.get(foodId).quantity++;
                    updateOrderSummary();
                }
            });
        });

        document.querySelectorAll('.remove-item').forEach(btn => {
            btn.addEventListener('click', function() {
                const foodId = this.dataset.foodId;
                orderItems.delete(foodId);
                updateOrderSummary();
            });
        });
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
            maximumFractionDigits: 0
        }).format(amount);
    }
});
</script>